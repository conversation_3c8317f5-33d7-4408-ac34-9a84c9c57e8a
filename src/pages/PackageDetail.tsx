import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Clock, 
  MapPin, 
  Star, 
  Users, 
  Car, 
  Utensils, 
  Camera,
  ArrowLeft,
  Check,
  X,
  Calendar,
  Phone,
  Shield,
  Wifi,
  Coffee,
  Mountain,
  Palmtree
} from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import sigiriyaImage from '@/assets/sigiriya-package.jpg';
import anuradhapuraImage from '@/assets/anuradhapura-package.jpg';

const PackageDetail = () => {
  const { packageId } = useParams();
  const whatsappNumber = "94771234567";

  // Mock data - in real app this would come from API/database
  const packageData = {
    'sigiriya-7-days': {
      id: 1,
      title: "Sigiriya 5 Days Cultural Tour",
      description: "Discover the ancient wonders of Sri Lanka's Cultural Triangle. Experience the majestic Sigiriya Rock Fortress, explore ancient temples, and immerse yourself in the rich heritage of this island nation.",
      image: sigiriyaImage,
      duration: "5 Days",
      price: "From $599",
      originalPrice: "$749",
      category: "Cultural Heritage",
      rating: 4.9,
      reviews: 142,
      maxGroupSize: 12,
      highlights: [
        "Climb the iconic Sigiriya Rock Fortress",
        "Explore Dambulla Cave Temple complex",
        "Visit the sacred Temple of the Tooth in Kandy",
        "Experience traditional Sri Lankan culture",
        "Professional photography opportunities"
      ],
      inclusions: [
        "Professional English-speaking guide",
        "Comfortable air-conditioned transportation", 
        "Daily breakfast and selected meals",
        "All entrance fees and permits",
        "4-star hotel accommodations",
        "Airport transfers",
        "Complimentary WiFi",
        "24/7 customer support",
        "Traditional cultural show",
        "Bottled water during tours"
      ],
      exclusions: [
        "International flights",
        "Visa fees",
        "Personal expenses",
        "Travel insurance",
        "Tips and gratuities",
        "Alcoholic beverages",
        "Lunch and dinner (unless specified)",
        "Optional activities"
      ],
      itinerary: [
        {
          day: 1,
          city: "Colombo",
          attraction: "Arrival & City Exploration",
          image: "https://images.unsplash.com/photo-1544970503-4836915d2d04?w=800&h=600&fit=crop",
          description: "Arrive at Bandaranaike International Airport where our representative will greet you. Transfer to Colombo for a comprehensive city tour including Independence Memorial Hall, Gangaramaya Temple, and the vibrant Pettah Markets. Experience the colonial charm of Fort district and enjoy a welcome dinner featuring authentic Sri Lankan cuisine.",
          activities: ["Airport pickup", "City tour", "Hotel check-in", "Welcome dinner"],
          accommodation: "Colombo City Hotel (4-star)",
          meals: "Dinner"
        },
        {
          day: 2,
          city: "Sigiriya", 
          attraction: "Lion Rock Fortress",
          image: sigiriyaImage,
          description: "Early morning departure to Sigiriya. Climb the spectacular 200-meter high Lion Rock Fortress, a UNESCO World Heritage site. Marvel at the ancient palace ruins, famous frescoes of celestial maidens, and enjoy panoramic views from the summit. Afternoon visit to a local village for cultural interaction and traditional lunch.",
          activities: ["Sigiriya Rock climb", "Frescoes viewing", "Village tour", "Cultural lunch"],
          accommodation: "Sigiriya Rock Resort (4-star)",
          meals: "Breakfast, Lunch, Dinner"
        },
        {
          day: 3,
          city: "Dambulla",
          attraction: "Cave Temple Complex", 
          image: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop",
          description: "Visit the magnificent Dambulla Cave Temple, housing over 150 Buddha statues and ancient murals dating back over 2,000 years. Each cave tells a different story of Buddhism in Sri Lanka. Afternoon spice garden visit to learn about Ceylon's famous spices and their medicinal properties.",
          activities: ["Cave temple exploration", "Buddhist art viewing", "Spice garden tour", "Herbal medicine workshop"],
          accommodation: "Dambulla Heritage Hotel (4-star)",
          meals: "Breakfast, Lunch, Dinner"
        },
        {
          day: 4,
          city: "Kandy",
          attraction: "Temple of the Tooth & Cultural Show",
          image: "https://images.unsplash.com/photo-1433086966358-54859d0ed716?w=800&h=600&fit=crop", 
          description: "Journey to the hill capital of Kandy, visiting the sacred Temple of the Tooth Relic, one of Buddhism's most important sites. Explore the Royal Botanical Gardens with its diverse collection of tropical plants. Evening cultural dance performance showcasing traditional Kandyan arts including fire dancing and drum performances.",
          activities: ["Temple of the Tooth visit", "Royal Botanical Gardens", "Cultural dance show", "Kandy Lake walk"],
          accommodation: "Kandy Palace Hotel (4-star)",
          meals: "Breakfast, Lunch, Dinner"
        },
        {
          day: 5,
          city: "Colombo",
          attraction: "Shopping & Departure",
          image: "https://images.unsplash.com/photo-1472396961693-142e6e269027?w=800&h=600&fit=crop",
          description: "Morning at leisure for last-minute shopping at Colombo's modern malls or traditional markets. Purchase authentic Sri Lankan souvenirs including tea, spices, and handicrafts. Transfer to airport for departure with beautiful memories of Sri Lanka's cultural heritage.",
          activities: ["Shopping tour", "Souvenir hunting", "Airport transfer", "Departure"],
          accommodation: "Day use hotel (if required)",
          meals: "Breakfast"
        }
      ]
    },
    'anuradhapura-9-days': {
      id: 2,
      title: "Anuradhapura 5 Days Ancient Heritage", 
      description: "Journey through Sri Lanka's ancient capital and explore UNESCO World Heritage sites with comprehensive cultural immersion and archaeological discoveries.",
      image: anuradhapuraImage,
      duration: "5 Days",
      price: "From $699", 
      originalPrice: "$849",
      category: "Ancient Heritage",
      rating: 4.8,
      reviews: 98,
      maxGroupSize: 10,
      highlights: [
        "Explore the ancient city of Anuradhapura",
        "Visit sacred Buddhist stupas and monasteries",
        "Discover archaeological wonders",
        "Experience traditional village life",
        "Expert archaeological guide"
      ],
      inclusions: [
        "Expert archaeological guide",
        "Premium transportation",
        "All meals included",
        "Entrance fees and permits", 
        "4-star accommodations",
        "Cultural workshops",
        "Traditional cooking class",
        "Meditation session",
        "Archaeological equipment",
        "All transfers"
      ],
      exclusions: [
        "International flights",
        "Visa fees",
        "Personal expenses",
        "Travel insurance",
        "Tips and gratuities",
        "Alcoholic beverages",
        "Optional activities",
        "Spa treatments"
      ],
      itinerary: [
        {
          day: 1,
          city: "Colombo",
          attraction: "Arrival & Historical Briefing",
          image: "https://images.unsplash.com/photo-1544970503-4836915d2d04?w=800&h=600&fit=crop",
          description: "Airport pickup and comprehensive orientation session about Sri Lanka's ancient history, archaeology, and cultural significance. Visit the National Museum for an introduction to the island's heritage.",
          activities: ["Airport pickup", "Museum visit", "Historical briefing", "Welcome dinner"],
          accommodation: "Colombo Heritage Hotel (4-star)",
          meals: "Dinner"
        },
        {
          day: 2,
          city: "Anuradhapura",
          attraction: "Sacred City Exploration",
          image: anuradhapuraImage,
          description: "Explore the ancient stupas including Ruwanwelisaya, Thuparamaya, and Jetavanaramaya, dating back over 2000 years. These magnificent structures represent the pinnacle of ancient Sri Lankan architecture and Buddhist devotion.",
          activities: ["Stupa visits", "Archaeological exploration", "Buddhist meditation", "Traditional lunch"],
          accommodation: "Anuradhapura Resort (4-star)",
          meals: "Breakfast, Lunch, Dinner"
        },
        {
          day: 3,
          city: "Polonnaruwa",
          attraction: "Medieval Capital",
          image: "https://images.unsplash.com/photo-1513836279014-a89f7a76ae86?w=800&h=600&fit=crop",
          description: "Discover the medieval capital of Polonnaruwa with its well-preserved ruins, including the Gal Vihara rock temple with its impressive Buddha statues carved from granite.",
          activities: ["Polonnaruwa ruins", "Gal Vihara visit", "Cycling tour", "Archaeological workshop"],
          accommodation: "Polonnaruwa Heritage Hotel (4-star)",
          meals: "Breakfast, Lunch, Dinner"
        },
        {
          day: 4,
          city: "Mihintale",
          attraction: "Birthplace of Buddhism",
          image: "https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=800&h=600&fit=crop",
          description: "Visit Mihintale, where Buddhism was first introduced to Sri Lanka. Climb the ancient steps to the summit for panoramic views and spiritual reflection.",
          activities: ["Mihintale climb", "Buddhist history lesson", "Village visit", "Cooking class"],
          accommodation: "Mihintale Mountain Resort (4-star)",
          meals: "Breakfast, Lunch, Dinner"
        },
        {
          day: 5,
          city: "Colombo",
          attraction: "Cultural Synthesis & Departure",
          image: "https://images.unsplash.com/photo-1472396961693-142e6e269027?w=800&h=600&fit=crop",
          description: "Final cultural synthesis session, shopping for authentic artifacts, and departure with deep understanding of Sri Lanka's ancient heritage.",
          activities: ["Cultural discussion", "Artifact shopping", "Airport transfer", "Departure"],
          accommodation: "Day use hotel (if required)",
          meals: "Breakfast"
        }
      ]
    }
  };

  const currentPackage = packageData[packageId as keyof typeof packageData];

  if (!currentPackage) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Package Not Found</h1>
          <Link to="/">
            <Button>Return Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  const handleBookNow = () => {
    const message = `Hi Camalanka Tours, I'm interested in the ${currentPackage.title}.`;
    window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');
  };

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative h-[50vh] md:h-[70vh] overflow-hidden">
        <img
          src={currentPackage.image}
          alt={currentPackage.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-black/20" />
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-4">
            <Link to="/" className="inline-flex items-center gap-2 text-white mb-4 md:mb-6 hover:text-camalanka-gold transition-colors text-sm md:text-base">
              <ArrowLeft className="w-4 h-4" />
              Back to Packages
            </Link>
            <h1 className="font-playfair text-2xl md:text-4xl lg:text-7xl font-bold text-white mb-4 md:mb-6 leading-tight">
              {currentPackage.title}
            </h1>
            <p className="text-sm md:text-xl text-white/90 max-w-3xl leading-relaxed">
              {currentPackage.description}
            </p>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 mt-4 md:mt-6">
              <Badge className="bg-camalanka-red text-white px-3 md:px-4 py-1 md:py-2 text-xs md:text-sm">
                {currentPackage.category}
              </Badge>
              <div className="flex items-center gap-2 text-white">
                <Star className="w-4 md:w-5 h-4 md:h-5 text-camalanka-gold fill-current" />
                <span className="font-semibold text-sm md:text-base">{currentPackage.rating}</span>
                <span className="text-white/80 text-sm md:text-base">({currentPackage.reviews} reviews)</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Package Content */}
      <section className="py-8 md:py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-12">
            
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6 md:space-y-12">
              
              {/* Package Overview */}
              <Card className="border-none shadow-elegant">
                <CardHeader className="pb-4">
                  <CardTitle className="font-playfair text-2xl md:text-3xl text-gray-800">Tour Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 md:space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                    <div className="flex items-center gap-2 md:gap-3 p-3 rounded-lg bg-gray-50">
                      <Clock className="w-5 md:w-6 h-5 md:h-6 text-camalanka-red flex-shrink-0" />
                      <div>
                        <div className="text-xs md:text-sm text-gray-600">Duration</div>
                        <div className="font-semibold text-sm md:text-base text-gray-800">{currentPackage.duration}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 md:gap-3 p-3 rounded-lg bg-gray-50">
                      <Users className="w-5 md:w-6 h-5 md:h-6 text-camalanka-red flex-shrink-0" />
                      <div>
                        <div className="text-xs md:text-sm text-gray-600">Max Group</div>
                        <div className="font-semibold text-sm md:text-base text-gray-800">{currentPackage.maxGroupSize} people</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 md:gap-3 p-3 rounded-lg bg-gray-50">
                      <MapPin className="w-5 md:w-6 h-5 md:h-6 text-camalanka-red flex-shrink-0" />
                      <div>
                        <div className="text-xs md:text-sm text-gray-600">Starting from</div>
                        <div className="font-semibold text-sm md:text-base text-gray-800">Colombo</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 md:gap-3 p-3 rounded-lg bg-gray-50">
                      <Car className="w-5 md:w-6 h-5 md:h-6 text-camalanka-red flex-shrink-0" />
                      <div>
                        <div className="text-xs md:text-sm text-gray-600">Transport</div>
                        <div className="font-semibold text-sm md:text-base text-gray-800">Included</div>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="font-playfair text-lg md:text-xl font-bold text-gray-800 mb-3 md:mb-4">Tour Highlights</h3>
                    <div className="grid grid-cols-1 gap-2 md:gap-3">
                      {currentPackage.highlights.map((highlight, index) => (
                        <div key={index} className="flex items-start gap-2 md:gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                          <Mountain className="w-4 md:w-5 h-4 md:h-5 text-camalanka-red mt-0.5 flex-shrink-0" />
                          <span className="text-sm md:text-base text-gray-700">{highlight}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Vertical Day-wise Itinerary */}
              <Card className="border-none shadow-elegant">
                <CardHeader className="pb-4">
                  <CardTitle className="font-playfair text-2xl md:text-3xl text-gray-800">Detailed Itinerary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6 md:space-y-8">
                    {currentPackage.itinerary.map((day, index) => (
                      <div key={index} className="relative">
                        {index !== currentPackage.itinerary.length - 1 && (
                          <div className="absolute left-4 md:left-6 top-12 md:top-16 w-0.5 h-full bg-gradient-to-b from-camalanka-red to-camalanka-gold opacity-30" />
                        )}
                        
                        <div className="flex gap-3 md:gap-6">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 md:w-12 md:h-12 bg-gradient-primary rounded-full flex items-center justify-center shadow-glow">
                              <span className="text-white font-bold text-sm md:text-base">{day.day}</span>
                            </div>
                          </div>
                          
                          <div className="flex-1">
                            <div className="grid grid-cols-1 gap-4 md:gap-6">
                              <div className="relative h-64 overflow-hidden rounded-lg shadow-elegant">
                                <img
                                  src={day.image}
                                  alt={day.attraction}
                                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
                                <div className="absolute bottom-4 left-4 text-white">
                                  <Badge className="bg-camalanka-red mb-2">Day {day.day}</Badge>
                                  <h4 className="font-playfair text-lg font-bold">{day.city}</h4>
                                </div>
                              </div>
                              
                              <div className="space-y-4">
                                <div>
                                  <h3 className="font-playfair text-2xl font-bold text-gray-800 mb-2">
                                    {day.attraction}
                                  </h3>
                                  <p className="text-gray-600 leading-relaxed">
                                    {day.description}
                                  </p>
                                </div>
                                
                                <div className="space-y-3">
                                  <div className="flex items-center gap-2">
                                    <Calendar className="w-4 h-4 text-camalanka-red" />
                                    <span className="text-sm text-gray-600">
                                      Activities: {day.activities.join(', ')}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Shield className="w-4 h-4 text-camalanka-red" />
                                    <span className="text-sm text-gray-600">
                                      Accommodation: {day.accommodation}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Utensils className="w-4 h-4 text-camalanka-red" />
                                    <span className="text-sm text-gray-600">
                                      Meals: {day.meals}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Inclusions & Exclusions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <Card className="border-none shadow-elegant">
                  <CardHeader>
                    <CardTitle className="font-playfair text-2xl text-gray-800 text-center">
                      What's Included
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {currentPackage.inclusions.map((inclusion, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <Check className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{inclusion}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-none shadow-elegant">
                  <CardHeader>
                    <CardTitle className="font-playfair text-2xl text-gray-800 text-center">
                      What's Not Included
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {currentPackage.exclusions.map((exclusion, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <X className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{exclusion}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Pricing Card */}
              <Card className="sticky top-4 border-none shadow-elegant">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <div className="flex items-center justify-center gap-3 mb-2">
                      <span className="text-2xl text-gray-400 line-through">{currentPackage.originalPrice}</span>
                      <Badge className="bg-green-600 text-white">Save 20%</Badge>
                    </div>
                    <div className="text-4xl font-bold text-camalanka-red mb-2">
                      {currentPackage.price}
                    </div>
                    <div className="text-gray-600">per person</div>
                  </div>
                  
                  <Separator className="my-6" />
                  
                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-3">
                      <Camera className="w-5 h-5 text-camalanka-red" />
                      <span className="text-sm">Professional photography spots</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Coffee className="w-5 h-5 text-camalanka-red" />
                      <span className="text-sm">Authentic local cuisine</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Wifi className="w-5 h-5 text-camalanka-red" />
                      <span className="text-sm">Complimentary WiFi</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="w-5 h-5 text-camalanka-red" />
                      <span className="text-sm">24/7 customer support</span>
                    </div>
                  </div>
                  
                  <Button 
                    onClick={handleBookNow}
                    className="w-full bg-gradient-primary hover:shadow-glow transition-smooth text-lg py-6 mb-4"
                  >
                    Book Now via WhatsApp
                  </Button>
                  
                  <div className="text-center space-y-2">
                    <p className="text-xs text-gray-500">
                      Free cancellation up to 24 hours before tour
                    </p>
                    <p className="text-xs text-gray-500">
                      Instant confirmation • Best price guarantee
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Info */}
              <Card className="border-none shadow-elegant">
                <CardHeader>
                  <CardTitle className="font-playfair text-xl text-gray-800">
                    Need Help?
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-camalanka-red" />
                    <div>
                      <div className="font-semibold text-gray-800">Call Us</div>
                      <div className="text-sm text-gray-600">+94 77 123 4567</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Palmtree className="w-5 h-5 text-camalanka-red" />
                    <div>
                      <div className="font-semibold text-gray-800">Expert Guides</div>
                      <div className="text-sm text-gray-600">Local knowledge & expertise</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default PackageDetail;