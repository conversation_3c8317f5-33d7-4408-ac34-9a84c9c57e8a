@tailwind base;
@tailwind components;
@tailwind utilities;

/* CAMALANKA TOURS Design System - Sri Lankan Tourism Theme */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 5%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 5%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 5%;

    /* Camalanka Red Theme */
    --primary: 0 100% 45%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 5%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 25 95% 53%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 100% 45%;

    --radius: 0.5rem;

    /* Custom Camalanka Colors */
    --camalanka-red: 0 100% 45%;
    --camalanka-red-dark: 0 100% 35%;
    --camalanka-gold: 45 95% 55%;
    --camalanka-emerald: 158 64% 52%;
    
    /* Beautiful Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--camalanka-red)), hsl(var(--camalanka-red-dark)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--camalanka-red) / 0.9), hsl(var(--camalanka-red-dark) / 0.8));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(0 0% 98%));
    
    /* Elegant Shadows */
    --shadow-elegant: 0 20px 40px -10px hsl(var(--camalanka-red) / 0.15);
    --shadow-card: 0 10px 30px -5px hsl(0 0% 0% / 0.1);
    --shadow-glow: 0 0 30px hsl(var(--camalanka-red) / 0.3);
    
    /* Smooth Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Animation Utilities */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out forwards;
}

.transition-smooth {
  transition: var(--transition-smooth);
}

.transition-bounce {
  transition: var(--transition-bounce);
}