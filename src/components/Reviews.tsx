import { Card, CardContent } from '@/components/ui/card';
import { Star, Quote } from 'lucide-react';

const Reviews = () => {
  const reviews = [
    {
      id: 1,
      name: "<PERSON>",
      country: "Australia",
      rating: 5,
      review: "An absolutely incredible experience! The Sigiriya tour was perfectly organized, and our guide was incredibly knowledgeable about Sri Lanka's history and culture. The accommodations were comfortable, and the food was amazing. I would definitely recommend Camalanka Tours to anyone visiting Sri Lanka.",
      tourTaken: "Sigiriya 5 Days Cultural Tour",
      date: "March 2024"
    },
    {
      id: 2,
      name: "<PERSON>",
      country: "Germany",
      rating: 5,
      review: "Professional service from start to finish. The ancient heritage tour exceeded all expectations. The archaeological guides were experts, and we learned so much about Sri Lanka's rich history. The vehicles were comfortable, and the itinerary was well-paced.",
      tourTaken: "Anuradhapura 5 Days Ancient Heritage",
      date: "February 2024"
    },
    {
      id: 3,
      name: "<PERSON><PERSON> Patel",
      country: "United Kingdom",
      rating: 5,
      review: "Camalanka Tours made our Sri Lankan adventure unforgettable. The attention to detail was remarkable, and the cultural experiences were authentic. Our driver was courteous and always on time. This was our first visit to Sri Lanka, and it certainly won't be our last!",
      tourTaken: "Sigiriya 5 Days Cultural Tour",
      date: "January 2024"
    },
    {
      id: 4,
      name: "<PERSON>",
      country: "Canada",
      rating: 5,
      review: "Exceptional value for money! The tour was comprehensive, covering all major attractions with enough time at each location. The WhatsApp booking system was convenient, and the customer service was responsive. The local insights from our guide made the experience truly special.",
      tourTaken: "Anuradhapura 5 Days Ancient Heritage",
      date: "December 2023"
    },
    {
      id: 5,
      name: "Emma Rodriguez",
      country: "Spain",
      rating: 5,
      review: "A perfect blend of adventure and culture. The accommodations were excellent, the food was delicious, and the transportation was comfortable. What impressed me most was the genuine care the team showed for our experience. Highly recommended!",
      tourTaken: "Sigiriya 5 Days Cultural Tour",
      date: "November 2023"
    },
    {
      id: 6,
      name: "Robert Thompson",
      country: "New Zealand",
      rating: 5,
      review: "Outstanding organization and attention to detail. The vehicle rental service was also top-notch - clean, well-maintained vehicles with professional drivers. The team at Camalanka Tours truly understands customer service. Will definitely use them again!",
      tourTaken: "Vehicle Rental Service",
      date: "October 2023"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-background to-muted">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-playfair text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            What Our Travelers Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our satisfied customers have to say about their Sri Lankan adventures with us.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review) => (
            <Card key={review.id} className="border-none shadow-elegant hover:shadow-glow transition-all duration-300 hover:scale-105">
              <CardContent className="p-8">
                <div className="flex items-center mb-4">
                  <Quote className="w-8 h-8 text-camalanka-red mb-4" />
                </div>
                
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className={`w-5 h-5 ${i < review.rating ? 'text-camalanka-gold fill-current' : 'text-gray-300'}`} 
                    />
                  ))}
                </div>

                <p className="text-gray-700 mb-6 leading-relaxed">
                  "{review.review}"
                </p>

                <div className="border-t pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-semibold text-gray-800">{review.name}</h4>
                      <p className="text-sm text-gray-600">{review.country}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{review.date}</p>
                    </div>
                  </div>
                  <p className="text-sm text-camalanka-red font-medium">
                    {review.tourTaken}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="inline-flex items-center gap-4 bg-white rounded-full px-8 py-4 shadow-elegant">
            <div className="flex items-center gap-2">
              <Star className="w-6 h-6 text-camalanka-gold fill-current" />
              <span className="text-2xl font-bold text-gray-800">4.9</span>
            </div>
            <div className="text-gray-600">
              <div className="font-semibold">Average Rating</div>
              <div className="text-sm">Based on 500+ reviews</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Reviews;