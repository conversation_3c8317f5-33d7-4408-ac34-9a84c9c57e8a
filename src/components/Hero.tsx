import { Button } from '@/components/ui/button';
import heroImage from '@/assets/hero-sri-lanka.jpg';

const Hero = () => {
  return (
    <section id="home" className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src={heroImage}
          alt="Sri Lankan Landscapes"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-hero"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
          Discover the Pearl of the Indian Ocean
        </h1>
        <p className="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in">
          Experience Sri Lanka's ancient wonders, pristine beaches, and rich culture with our expertly crafted tour packages
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in">
          <Button
            size="lg"
            className="bg-white text-camalanka-red hover:bg-gray-100 hover:shadow-glow transition-bounce text-lg px-8 py-4"
            onClick={() => document.getElementById('packages')?.scrollIntoView({ behavior: 'smooth' })}
          >
            Explore Packages
          </Button>
          <Button
            size="lg"
            className="border-2 border-white bg-transparent text-white hover:bg-white hover:text-camalanka-red transition-bounce text-lg px-8 py-4"
            onClick={() => document.getElementById('vehicles')?.scrollIntoView({ behavior: 'smooth' })}
          >
            Book a Vehicle
          </Button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;