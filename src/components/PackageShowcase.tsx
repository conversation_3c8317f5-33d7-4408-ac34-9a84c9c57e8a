import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, MapPin, Star } from 'lucide-react';
import { Link } from 'react-router-dom';
import sigiriyaImage from '@/assets/sigiriya-package.jpg';
import anuradhapuraImage from '@/assets/anuradhapura-package.jpg';

const PackageShowcase = () => {
  const whatsappNumber = "94771234567"; // Replace with actual number

  const packages = [
    {
      id: 1,
      title: "Sigiriya 7 Days Tour",
      slug: "sigiriya-7-days",
      description: "Explore the ancient rock fortress of Sigiriya and discover the cultural triangle of Sri Lanka",
      image: sigiriyaImage,
      duration: "7 Days",
      price: "From $799",
      highlights: ["Sigiriya Rock Fortress", "Dambulla Cave Temples", "Kandy Temple", "Elephant Orphanage"],
      rating: 4.9
    },
    {
      id: 2,
      title: "Anuradhapura 9 Days Tour",
      slug: "anuradhapura-9-days",
      description: "Journey through the ancient capital and explore UNESCO World Heritage sites",
      image: anuradhapuraImage,
      duration: "9 Days", 
      price: "From $1099",
      highlights: ["Ancient Stupas", "Sacred Bo Tree", "Ruins of Anuradhapura", "Mihintale Temple"],
      rating: 4.8
    },
    {
      id: 3,
      title: "Colombo & Coastal 5 Days",
      slug: "colombo-coastal-5-days",
      description: "Experience modern Sri Lanka and pristine coastal beaches",
      image: sigiriyaImage, // Placeholder - will be replaced
      duration: "5 Days",
      price: "From $599",
      highlights: ["Colombo City Tour", "Galle Fort", "Beach Relaxation", "Local Markets"],
      rating: 4.7
    },
    {
      id: 4,
      title: "Hill Country Adventure",
      slug: "hill-country-adventure",
      description: "Tea plantations, waterfalls, and cool mountain air",
      image: anuradhapuraImage, // Placeholder - will be replaced
      duration: "6 Days",
      price: "From $699",
      highlights: ["Nuwara Eliya", "Tea Factory Visits", "Adam's Peak", "Ella Rock"],
      rating: 4.9
    }
  ];

  const handleBookNow = (packageTitle: string) => {
    const message = `Hi Camalanka Tours, I'm interested in the ${packageTitle}.`;
    window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');
  };

  return (
    <section id="packages" className="py-20 bg-secondary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Featured Tour Packages
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Carefully crafted experiences that showcase the best of Sri Lanka's culture, history, and natural beauty
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {packages.map((pkg) => (
            <Card key={pkg.id} className="overflow-hidden shadow-card hover:shadow-elegant transition-smooth group">
              <Link to={`/package/${pkg.slug}`}>
                <div className="relative overflow-hidden">
                  <img
                    src={pkg.image}
                    alt={pkg.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-smooth"
                  />
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-camalanka-gold fill-current" />
                      <span className="text-sm font-semibold">{pkg.rating}</span>
                    </div>
                  </div>
                </div>

                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-gray-800 hover:text-camalanka-red transition-colors">{pkg.title}</CardTitle>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {pkg.duration}
                    </div>
                    <div className="text-camalanka-red font-bold">{pkg.price}</div>
                  </div>
                </CardHeader>

                <CardContent className="pb-2">
                  <p className="text-gray-600 text-sm mb-3">{pkg.description}</p>
                  <div className="space-y-1">
                    {pkg.highlights.slice(0, 3).map((highlight, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                        <MapPin className="w-3 h-3 text-camalanka-red" />
                        {highlight}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Link>

              <CardFooter className="pt-4">
                <div className="w-full space-y-2">
                  <Link to={`/package/${pkg.slug}`} className="block">
                    <Button
                      variant="outline"
                      className="w-full border-camalanka-red text-camalanka-red hover:bg-camalanka-red hover:text-white transition-smooth"
                    >
                      View Details
                    </Button>
                  </Link>
                  <Button
                    onClick={() => handleBookNow(pkg.title)}
                    className="w-full bg-gradient-primary hover:shadow-glow transition-smooth"
                  >
                    Book Now
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button
            variant="outline"
            size="lg"
            className="border-camalanka-red text-camalanka-red hover:bg-camalanka-red hover:text-white transition-smooth"
          >
            View All Packages
          </Button>
        </div>
      </div>
    </section>
  );
};

export default PackageShowcase;