import { Phone, Mail, MapPin, MessageCircle, Facebook, Instagram, Twitter } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Footer = () => {
  const whatsappNumber = "94776701792";
  const whatsappMessage = "Hi Casper Tours, I'd like to get more information about your services.";

  const quickLinks = [
    { name: 'About Us', href: '#about' },
    { name: 'Tour Packages', href: '#packages' },
    { name: 'Vehicle Rental', href: '#vehicles' },
    { name: 'Contact Us', href: '#contact' },
  ];

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
  ];

  return (
    <footer id="contact" className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <h3 className="text-3xl font-bold mb-4">
              CASPER<span className="text-camalanka-gold">TOURS</span>
            </h3>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Your trusted partner for exploring the beautiful island of Sri Lanka. We specialize in creating 
              unforgettable experiences that showcase the rich culture, stunning landscapes, and warm hospitality 
              of the Pearl of the Indian Ocean.
            </p>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-camalanka-red" />
                <span>+94 77 670 1792</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-camalanka-red" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="w-5 h-5 text-camalanka-red" />
                <span>Abagahawaththa ahangama road, Imaduwa</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-camalanka-red transition-smooth"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact & Social */}
          <div>
            <h4 className="text-xl font-semibold mb-6">Connect With Us</h4>
            
            <Button
              onClick={() => window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(whatsappMessage)}`, '_blank')}
              className="w-full mb-6 bg-green-600 hover:bg-green-700 transition-smooth"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              WhatsApp Us
            </Button>

            <div className="flex gap-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-camalanka-red transition-smooth"
                >
                  <social.icon className="w-5 h-5" />
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 mb-4 md:mb-0">
              © 2024 Casper Tours. All rights reserved.
            </p>
            <div className="flex gap-6 text-sm text-gray-400">
              <a href="#" className="hover:text-white transition-smooth">Privacy Policy</a>
              <a href="#" className="hover:text-white transition-smooth">Terms of Service</a>
              <a href="#" className="hover:text-white transition-smooth">Sitemap</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;