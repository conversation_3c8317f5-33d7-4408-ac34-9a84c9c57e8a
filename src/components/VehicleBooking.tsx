import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Users, Shield, Clock, MessageCircle, MapPin, Plane, Car, DollarSign } from 'lucide-react';
import { useState } from 'react';
import Autoplay from "embla-carousel-autoplay";
const carReal1 = '/lovable-uploads/55614362-0100-43cc-be41-5eec8326f7a0.png';
const carReal2 = '/lovable-uploads/66fe45f4-3cc2-4d74-a9cb-ccc9fc5e70d9.png';
const carReal3 = '/lovable-uploads/3c0d80c0-02bf-4cb3-898d-6db61d1f2383.png';
const carReal4 = '/lovable-uploads/6272fe1f-4d26-488a-887c-501755a787f1.png';
const carReal5 = '/lovable-uploads/28ba7644-5540-4216-87f5-daf4a0882462.png';

const VehicleBooking = () => {
  const whatsappNumber = "94776701792";
  const [bookingData, setBookingData] = useState({
    name: '',
    phone: '',
    email: '',
    pickupLocation: '',
    dropoffLocation: '',
    date: '',
    time: '',
    serviceType: 'airport-transfer',
    additionalInfo: ''
  });

  const ratePerKm = 0.34; // USD per KM

  const handleInputChange = (field: string, value: string) => {
    setBookingData(prev => ({ ...prev, [field]: value }));
  };

  const handleBookingSubmit = () => {
    const message = `🚗 Car Booking Request - Casper Tours

👤 Name: ${bookingData.name}
📞 Phone: ${bookingData.phone}
📧 Email: ${bookingData.email}

🚩 Service: ${bookingData.serviceType === 'airport-transfer' ? 'Airport Transfer' : 'Tour Transport'}
📍 Pickup: ${bookingData.pickupLocation}
📍 Drop-off: ${bookingData.dropoffLocation}
📅 Date: ${bookingData.date}
⏰ Time: ${bookingData.time}

💰 Rate: $${ratePerKm}/KM

${bookingData.additionalInfo ? `📝 Additional Info: ${bookingData.additionalInfo}` : ''}

Please confirm availability and total cost. Thank you!`;

    window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');
  };

  return (
    <section id="vehicles" className="py-20 bg-secondary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Car Transport Service
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Professional car transport for airport transfers and island tours with transparent pricing
          </p>
        </div>

        {/* Car Carousel & Rate Highlight */}
        <div className="mb-16 relative overflow-hidden rounded-2xl shadow-elegant">
          <Carousel 
            className="w-full"
            plugins={[
              Autoplay({
                delay: 3000,
              }),
            ]}
          >
            <CarouselContent>
              <CarouselItem>
                <img
                  src={carReal1}
                  alt="Casper Tours - Professional Toyota Prius Service"
                  className="w-full h-64 md:h-80 object-cover"
                />
              </CarouselItem>
              <CarouselItem>
                <img
                  src={carReal2}
                  alt="Casper Tours - Reliable Airport Transfer"
                  className="w-full h-64 md:h-80 object-cover"
                />
              </CarouselItem>
              <CarouselItem>
                <img
                  src={carReal3}
                  alt="Casper Tours - Modern Hybrid Vehicle"
                  className="w-full h-64 md:h-80 object-cover"
                />
              </CarouselItem>
              <CarouselItem>
                <img
                  src={carReal4}
                  alt="Casper Tours - Clean & Comfortable Cars"
                  className="w-full h-64 md:h-80 object-cover"
                />
              </CarouselItem>
              <CarouselItem>
                <img
                  src={carReal5}
                  alt="Casper Tours - Tourist Transport Service"
                  className="w-full h-64 md:h-80 object-cover"
                />
              </CarouselItem>
            </CarouselContent>
            <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2" />
            <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2" />
          </Carousel>
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent flex items-center pointer-events-none">
            <div className="text-white p-8">
              <h3 className="text-2xl md:text-3xl font-bold mb-2">Modern & Reliable Cars</h3>
              <p className="text-lg opacity-90 mb-4">Air-conditioned vehicles with professional drivers</p>
              <div className="bg-camalanka-red/90 backdrop-blur-sm rounded-lg p-4 inline-block">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-6 h-6" />
                  <span className="text-2xl font-bold">${ratePerKm} USD per KM</span>
                </div>
                <p className="text-sm opacity-90">Transparent pricing • No hidden fees</p>
              </div>
            </div>
          </div>
        </div>

        {/* Car Details Section */}
        <div className="mb-12">
          <div className="space-y-8">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-800">
                  <Car className="w-5 h-5 text-camalanka-red" />
                  Our Car Service
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-camalanka-red" />
                    <span className="text-gray-700">Up to 4 Passengers</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Shield className="w-5 h-5 text-camalanka-emerald" />
                    <span className="text-gray-700">Fully Insured</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-camalanka-gold" />
                    <span className="text-gray-700">24/7 Available</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-camalanka-blue" />
                    <span className="text-gray-700">Island Wide</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Service Types - Full Width Left and Right */}
            <div className="grid md:grid-cols-2 gap-8 w-full">
              <Card className="bg-blue-50 border-blue-200 shadow-card">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Plane className="w-8 h-8 text-blue-600" />
                    <h3 className="text-2xl font-bold text-blue-900">Airport Transfers</h3>
                  </div>
                  <p className="text-blue-700 text-lg">Pick-up and drop-off service to/from Colombo Airport (CMB)</p>
                </CardContent>
              </Card>
              
              <Card className="bg-green-50 border-green-200 shadow-card">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <MapPin className="w-8 h-8 text-green-600" />
                    <h3 className="text-2xl font-bold text-green-900">Tour Transport</h3>
                  </div>
                  <p className="text-green-700 text-lg">Comfortable transport to all tourist destinations across Sri Lanka</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Full Width Booking Form with Enhanced Background */}
        <div className="relative">
          {/* Background with gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-camalanka-red/10 via-camalanka-blue/5 to-camalanka-emerald/10 rounded-3xl"></div>
          <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-3xl"></div>

          <Card className="relative shadow-elegant max-w-4xl mx-auto border-2 border-camalanka-red/20 bg-white/90 backdrop-blur-sm">
            <CardHeader className="text-center bg-gradient-to-r from-camalanka-red to-camalanka-red-dark text-white rounded-t-xl">
              <CardTitle className="flex items-center justify-center gap-2 text-white text-2xl">
                <MessageCircle className="w-6 h-6" />
                Book Your Car
              </CardTitle>
              <p className="text-lg text-white/90">Fill out the form below and we'll contact you via WhatsApp</p>
            </CardHeader>
          <CardContent className="space-y-6 bg-gradient-to-br from-white to-gray-50/50 p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name" className="text-base font-semibold text-gray-800">Full Name *</Label>
                <Input
                  id="name"
                  value={bookingData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Your full name"
                  className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
                />
              </div>
              <div>
                <Label htmlFor="phone" className="text-base font-semibold text-gray-800">Phone Number *</Label>
                <Input
                  id="phone"
                  value={bookingData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+94 XX XXX XXXX"
                  className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="email" className="text-base font-semibold text-gray-800">Email</Label>
              <Input
                id="email"
                type="email"
                value={bookingData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
              />
            </div>

            <div>
              <Label htmlFor="serviceType" className="text-base font-semibold text-gray-800">Service Type *</Label>
              <Select value={bookingData.serviceType} onValueChange={(value) => handleInputChange('serviceType', value)}>
                <SelectTrigger className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="airport-transfer">Airport Transfer</SelectItem>
                  <SelectItem value="tour-transport">Tour Transport</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="pickup" className="text-base font-semibold text-gray-800">Pickup Location *</Label>
                <Input
                  id="pickup"
                  value={bookingData.pickupLocation}
                  onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                  placeholder="Hotel, Airport, etc."
                  className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
                />
              </div>
              <div>
                <Label htmlFor="dropoff" className="text-base font-semibold text-gray-800">Drop-off Location *</Label>
                <Input
                  id="dropoff"
                  value={bookingData.dropoffLocation}
                  onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                  placeholder="Destination"
                  className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="date" className="text-base font-semibold text-gray-800">Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={bookingData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
                />
              </div>
              <div>
                <Label htmlFor="time" className="text-base font-semibold text-gray-800">Time *</Label>
                <Input
                  id="time"
                  type="time"
                  value={bookingData.time}
                  onChange={(e) => handleInputChange('time', e.target.value)}
                  className="h-12 text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="additionalInfo" className="text-base font-semibold text-gray-800">Additional Information</Label>
              <Textarea
                id="additionalInfo"
                value={bookingData.additionalInfo}
                onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
                placeholder="Special requests, number of passengers, luggage details, etc."
                rows={4}
                className="text-base border-2 border-gray-200 focus:border-camalanka-red focus:ring-camalanka-red/20"
              />
            </div>

            <div className="bg-gradient-to-r from-camalanka-red/10 to-camalanka-blue/10 border-2 border-camalanka-red/20 p-6 rounded-xl">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-6 h-6 text-camalanka-red" />
                <span className="font-bold text-gray-800 text-lg">Pricing: ${ratePerKm} USD per KM</span>
              </div>
              <p className="text-gray-700 font-medium">Final cost will be calculated based on actual distance traveled</p>
            </div>

            <Button
              onClick={handleBookingSubmit}
              className="w-full h-14 bg-gradient-to-r from-camalanka-red to-camalanka-red-dark hover:from-camalanka-red-dark hover:to-camalanka-red text-white hover:shadow-glow transition-smooth text-lg font-semibold"
              disabled={!bookingData.name || !bookingData.phone || !bookingData.pickupLocation || !bookingData.dropoffLocation || !bookingData.date || !bookingData.time}
            >
              <MessageCircle className="w-5 h-5 mr-2" />
              Send WhatsApp Booking Request
            </Button>
          </CardContent>
        </Card>
        </div>
      </div>
    </section>
  );
};

export default VehicleBooking;