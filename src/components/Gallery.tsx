import { Button } from '@/components/ui/button';

const Gallery = () => {
  const carReal1 = '/lovable-uploads/55614362-0100-43cc-be41-5eec8326f7a0.png';
  const carReal2 = '/lovable-uploads/66fe45f4-3cc2-4d74-a9cb-ccc9fc5e70d9.png';
  const carReal3 = '/lovable-uploads/3c0d80c0-02bf-4cb3-898d-6db61d1f2383.png';
  const carReal4 = '/lovable-uploads/6272fe1f-4d26-488a-887c-501755a787f1.png';
  const carReal5 = '/lovable-uploads/28ba7644-5540-4216-87f5-daf4a0882462.png';

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Fleet Gallery
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Take a look at our well-maintained vehicles and the beautiful destinations we'll take you to
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Main featured image - spans 2 columns on large screens */}
          <div className="lg:col-span-2 lg:row-span-2">
            <div className="relative h-64 lg:h-full overflow-hidden rounded-2xl shadow-elegant group">
              <img
                src={carReal1}
                alt="Casper Tours - Premium Toyota Prius Fleet"
                className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-smooth" />
              <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-smooth">
                <h3 className="text-xl font-bold">Premium Fleet</h3>
                <p className="text-sm opacity-90">Modern & Comfortable Vehicles</p>
              </div>
            </div>
          </div>

          {/* Gallery images */}
          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src={carReal2}
              alt="Casper Tours Vehicle 2"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src={carReal3}
              alt="Casper Tours Vehicle 3"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src={carReal4}
              alt="Casper Tours Vehicle 4"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src={carReal5}
              alt="Casper Tours Vehicle 5"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          {/* Additional images for the remaining 6 images */}
          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src="/lovable-uploads/gallery-6.jpg"
              alt="Casper Tours Gallery 6"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="lg:col-span-2 relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src="/lovable-uploads/gallery-7.jpg"
              alt="Casper Tours Gallery 7"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src="/lovable-uploads/gallery-8.jpg"
              alt="Casper Tours Gallery 8"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src="/lovable-uploads/gallery-9.jpg"
              alt="Casper Tours Gallery 9"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src="/lovable-uploads/gallery-10.jpg"
              alt="Casper Tours Gallery 10"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-32 lg:h-40 overflow-hidden rounded-xl shadow-card group">
            <img
              src="/lovable-uploads/gallery-11.jpg"
              alt="Casper Tours Gallery 11"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>
        </div>

        {/* Gallery CTA */}
        <div className="text-center mt-12">
          <div className="bg-gradient-card p-8 rounded-2xl shadow-card max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Ready to Experience Sri Lanka?</h3>
            <p className="text-gray-600 mb-6">Book your vehicle today and start your unforgettable journey across the Pearl of the Indian Ocean</p>
            <Button
              onClick={() => document.getElementById('vehicles')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-camalanka-red hover:bg-camalanka-red-dark text-white hover:shadow-glow transition-smooth px-8 py-3"
            >
              Book Your Vehicle Now
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Gallery;