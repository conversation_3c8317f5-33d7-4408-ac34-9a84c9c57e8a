import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { X } from 'lucide-react';

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const carReal1 = '/lovable-uploads/gallery/10.jpeg';
  const carReal2 = '/lovable-uploads/gallery/11.jpeg';
  const carReal3 = '/lovable-uploads/gallery/12.jpeg';
  const carReal4 = '/lovable-uploads/gallery/13.jpeg';
  const carReal5 = '/lovable-uploads/gallery/14.jpeg';
  const carReal6 = '/lovable-uploads/gallery/15.jpeg';
  const carReal7 = '/lovable-uploads/gallery/16.jpeg';
  const carReal8 = '/lovable-uploads/gallery/17.jpeg';
  const carReal9 = '/lovable-uploads/gallery/18.jpeg';
  const carReal10 = '/lovable-uploads/gallery/19.jpeg';
  const carReal11 = '/lovable-uploads/gallery/20.jpeg';



  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Fleet Gallery
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Take a look at our well-maintained vehicles and the beautiful destinations we'll take you to
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Main featured image - spans 2 columns on large screens */}
          <div className="lg:col-span-2 lg:row-span-3">
            <div className="relative h-64 lg:h-full overflow-hidden rounded-2xl shadow-elegant group cursor-pointer">
              <img
                src={carReal1}
                alt="Casper Tours - Premium Toyota Prius Fleet"
                className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
                onClick={() => setSelectedImage(carReal1)}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-smooth" />
              <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-smooth">
                <h3 className="text-xl font-bold">Premium Fleet</h3>
                <p className="text-sm opacity-90">Modern & Comfortable Vehicles</p>
              </div>
            </div>
          </div>

          {/* Gallery images - increased height for better balance */}
          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal2}
              alt="Casper Tours Vehicle 2"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal2)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal3}
              alt="Casper Tours Vehicle 3"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal3)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal4}
              alt="Casper Tours Vehicle 4"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal4)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          {/* Additional images using carReal5 through carReal11 */}
          <div className="lg:col-span-2 relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal5}
              alt="Casper Tours Vehicle 7"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal5)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal6}
              alt="Casper Tours Vehicle 6"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal6)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="lg:col-span-2 relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal7}
              alt="Casper Tours Vehicle 7"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal7)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal8}
              alt="Casper Tours Vehicle 8"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal8)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal9}
              alt="Casper Tours Vehicle 9"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal9)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal10}
              alt="Casper Tours Vehicle 10"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal10)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>

          <div className="relative h-40 lg:h-48 overflow-hidden rounded-xl shadow-card group cursor-pointer">
            <img
              src={carReal11}
              alt="Casper Tours Vehicle 11"
              className="w-full h-full object-cover group-hover:scale-110 transition-smooth"
              onClick={() => setSelectedImage(carReal11)}
            />
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-smooth" />
          </div>
        </div>

        {/* Gallery CTA */}
        <div className="text-center mt-12">
          <div className="bg-gradient-card p-8 rounded-2xl shadow-card max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Ready to Experience Sri Lanka?</h3>
            <p className="text-gray-600 mb-6">Book your vehicle today and start your unforgettable journey across the Pearl of the Indian Ocean</p>
            <Button
              onClick={() => document.getElementById('vehicles')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-camalanka-red hover:bg-camalanka-red-dark text-white hover:shadow-glow transition-smooth px-8 py-3"
            >
              Book Your Vehicle Now
            </Button>
          </div>
        </div>
      </div>

      {/* Image Modal/Lightbox */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-7xl max-h-full">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <X size={32} />
            </button>
            <img
              src={selectedImage}
              alt="Gallery Image"
              className="max-w-full max-h-[90vh] object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default Gallery;